"""
Stage 6: Test Script Generation for GretahAI ScriptWeaver

This module handles the test script generation phase of the application workflow.
It provides functionality for:
- Generating test scripts for selected test case steps using Google AI
- Two-phase script generation process (isolated script + merging with previous steps)
- Script file management with unique timestamped filenames
- Multiple script views (merged, step-specific, diff, origins)
- Integration with test data and element matches from previous stages
- Proper workflow transitions to Stage 7 (Run Script)

The stage supports both merged script generation that maintains continuity with
previous steps and step-specific script generation for isolated testing.
All Google AI API calls are routed through the centralized generate_llm_response
function in core/ai.py for consistent logging and error handling.

Functions:
    stage6_generate_script(state): Main Stage 6 function for test script generation
"""

import os
import logging
import streamlit as st
from datetime import datetime
import time
from state_manager import StateStage

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage6")


def create_final_step_script(state):
    """
    Create a script file containing only the final step for the current test case.

    This function creates a script with only the last step's implementation, which is
    what should be optimized in Stage 8. The final step script already contains all
    the necessary context and continuity from previous steps.

    Args:
        state (StateManager): The application state manager instance

    Returns:
        str: Path to the final step script file, or None if creation failed
    """
    try:
        # Check if we have a test case and previous scripts
        if not hasattr(state, 'selected_test_case') or not state.selected_test_case:
            logger.warning("No selected test case found, cannot create final step script")
            return None

        if not hasattr(state, 'previous_scripts') or not state.previous_scripts:
            logger.warning("No previous scripts found, cannot create final step script")
            return None

        # Get the test case ID
        test_case_id = state.selected_test_case.get('Test Case ID', 'unknown')

        # Get all step numbers and find the highest (final) step
        step_numbers = sorted(state.previous_scripts.keys(), key=int)

        if not step_numbers:
            logger.warning("No step scripts found, cannot create final step script")
            return None

        # Get the final step number and its script
        final_step_no = step_numbers[-1]  # Last step in sorted order
        final_step_script = state.previous_scripts[final_step_no]

        logger.info(f"Stage 6: Creating final step script for test case {test_case_id}, final step: {final_step_no}")

        # Add a header comment to the final step script
        header = f"""# Final Step Script for Test Case: {test_case_id}
# Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
# Final step: {final_step_no} (contains all previous step context)
# Created in Stage 6 (Script Generation) for Stage 8 optimization
"""
        final_script_content = header + final_step_script

        # Create a file path for the final step script
        script_dir = "generated_tests"
        os.makedirs(script_dir, exist_ok=True)
        final_script_file = os.path.join(
            script_dir,
            f"test_{test_case_id}_final_step_{final_step_no}_{int(time.time())}.py"
        )

        # Save the final step script to a file
        with open(final_script_file, "w") as f:
            f.write(final_script_content)

        # Store the final step script content and path in the state for Stage 8
        state.combined_script_content = final_script_content
        state.combined_script_path = final_script_file
        logger.info(f"Stage 6: Stored final step script content in state for Stage 8 (length: {len(final_script_content)} chars)")
        logger.info(f"Stage 6: Stored final step script path in state: {final_script_file}")

        logger.info(f"Stage 6: Successfully created final step script file: {final_script_file}")
        return final_script_file

    except Exception as e:
        logger.error(f"Stage 6: Error creating final step script: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None


def create_combined_script(state):
    """
    Create a combined script file containing all steps for the current test case.

    This function is called from Stage 6 when script generation is complete for all steps.
    It merges all individual step scripts in the correct order using simple concatenation.

    NOTE: For Stage 8 optimization, use create_final_step_script() instead, which only
    includes the final step's script (as the final step already contains all context).

    Args:
        state (StateManager): The application state manager instance

    Returns:
        str: Path to the combined script file, or None if creation failed
    """
    try:
        # Check if we have a test case and previous scripts
        if not hasattr(state, 'selected_test_case') or not state.selected_test_case:
            logger.warning("No selected test case found, cannot create combined script")
            return None

        if not hasattr(state, 'previous_scripts') or not state.previous_scripts:
            logger.warning("No previous scripts found, cannot create combined script")
            return None

        # Get the test case ID
        test_case_id = state.selected_test_case.get('Test Case ID', 'unknown')

        # Get all step numbers in order
        step_numbers = sorted(state.previous_scripts.keys(), key=int)

        if not step_numbers:
            logger.warning("No step scripts found, cannot create combined script")
            return None

        logger.info(f"Stage 6: Creating combined script for test case {test_case_id} with steps: {', '.join(step_numbers)}")

        # Start with the first step's script
        combined_script = state.previous_scripts[step_numbers[0]]

        # Add a header comment to the combined script
        header = f"""# Combined Test Script for Test Case: {test_case_id}
# Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
# Includes steps: {', '.join(step_numbers)}
# Created in Stage 6 (Script Generation)
"""
        combined_script = header + combined_script

        # Concatenate each subsequent step's script (simple approach for Stage 6)
        for i in range(1, len(step_numbers)):
            current_step = step_numbers[i]
            current_script = state.previous_scripts[current_step]

            # Use simple concatenation in Stage 6 (AI optimization happens in Stage 8)
            logger.info(f"Stage 6: Concatenating step {current_step} into combined script")
            combined_script += f"\n\n# --- STEP {current_step} ---\n{current_script}"

        # Create a file path for the combined script
        script_dir = "generated_tests"
        os.makedirs(script_dir, exist_ok=True)
        combined_script_file = os.path.join(
            script_dir,
            f"test_{test_case_id}_combined_{int(time.time())}.py"
        )

        # Save the combined script to a file
        with open(combined_script_file, "w") as f:
            f.write(combined_script)

        # Store the combined script content and path in the state for Stage 8
        state.combined_script_content = combined_script
        state.combined_script_path = combined_script_file
        logger.info(f"Stage 6: Stored combined script content in state for Stage 8 (length: {len(combined_script)} chars)")
        logger.info(f"Stage 6: Stored combined script path in state: {combined_script_file}")

        logger.info(f"Stage 6: Successfully created combined script file: {combined_script_file}")
        return combined_script_file

    except Exception as e:
        logger.error(f"Stage 6: Error creating combined script: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None


def stage6_generate_script(state):
    """
    Phase 6: Generate Test Script for Selected Test Case Step.

    This stage allows the user to generate a test script for the selected test case step.
    It checks if all prerequisites are met (step selection, element matching, test data),
    displays the selected step information, and provides options for script generation.
    When the user clicks the generate button, it calls the AI to generate a test script
    and displays the result.

    Args:
        state (StateManager): The application state manager instance
    """
    # Import time module locally to avoid shadowing
    import time

    st.markdown("<h2 class='stage-header'>Phase 6: Generate Test Script</h2>", unsafe_allow_html=True)

    # Check prerequisites with specific error messages
    if not hasattr(state, 'selected_step') or not state.selected_step:
        st.warning("⚠️ Please select a test case step in Phase 4 first")
        return

    if not hasattr(state, 'step_matches') and not hasattr(state, 'element_matches'):
        st.warning("⚠️ Please complete element matching in Phase 4 first")
        return

    # Check if test data is required but not configured
    llm_step_analysis = getattr(state, 'llm_step_analysis', {})
    test_data_skipped = getattr(state, 'test_data_skipped', False)

    if llm_step_analysis.get("requires_test_data", False) and not test_data_skipped and not hasattr(state, 'test_data'):
        st.warning("⚠️ Please configure test data in Phase 5 first")
        return

    # Get the selected step information
    selected_step = state.selected_step
    selected_step_table_entry = getattr(state, 'selected_step_table_entry', None)

    # Display step information in a compact format
    col1, col2 = st.columns(2)
    with col1:
        st.markdown(f"**Step {selected_step.get('Step No')}:** {selected_step.get('Test Steps')}")
    with col2:
        st.markdown(f"**Expected:** {selected_step.get('Expected Result')}")

    # Show element matches and test data in collapsed expanders only if available
    if hasattr(state, 'element_matches') and state.element_matches:
        try:
            test_case_id = state.selected_test_case.get('Test Case ID')
            step_no = str(selected_step.get('Step No'))
            step_matches = state.element_matches.get(test_case_id, {}).get(step_no, [])

            if step_matches:
                with st.expander(f"Element Matches ({len(step_matches)})", expanded=False):
                    for i, match in enumerate(step_matches):
                        element = match.get('element', {})
                        st.markdown(f"**{i+1}.** {element.get('name', 'Unknown')} - {match.get('action', 'Unknown')}")
        except (KeyError, TypeError, AttributeError) as e:
            logger.error(f"Error displaying element matches: {str(e)}")

    if hasattr(state, 'test_data') and state.test_data:
        with st.expander(f"Test Data ({len(state.test_data)} items)", expanded=False):
            for key, value in state.test_data.items():
                if 'step_' in key:
                    step_num = key.split('step_')[1]
                    st.markdown(f"**Step {step_num}:** `{value}`")
                else:
                    st.markdown(f"**{key}:** `{value}`")

    # Script generation section
    st.subheader("Generate Script")

    # Check if this is a regeneration attempt
    is_regeneration = st.session_state.get('regeneration_in_progress', False)

    # Get regeneration attempt from state (primary source) and session state (backup)
    state_regen_count = getattr(state, 'script_regeneration_count', 0)
    session_regen_count = st.session_state.get('regeneration_attempt', 0)

    # Use state as the authoritative source, but sync with session state
    regeneration_attempt = state_regen_count

    # Sync session state with state manager (state manager is authoritative)
    if state_regen_count != session_regen_count:
        st.session_state['regeneration_attempt'] = state_regen_count
        logger.info(f"Synced session regeneration_attempt: {session_regen_count} → {state_regen_count}")

    feedback_guidelines_count = st.session_state.get('feedback_guidelines_count', 0)

    if is_regeneration:
        st.info(f"🔄 Regeneration Mode - Attempt #{regeneration_attempt} with {feedback_guidelines_count} guidelines")
        # Clear regeneration flags after displaying
        st.session_state['regeneration_in_progress'] = False
        logger.info("Cleared regeneration_in_progress flag after displaying regeneration status")

    # Add a button to generate the test script
    button_text = f"🔄 Regenerate Script (Attempt #{regeneration_attempt + 1})" if regeneration_attempt > 0 else "Generate Test Script"
    if st.button(button_text, key="generate_script_btn", use_container_width=True):

        # Track regeneration if this is a regeneration attempt
        if regeneration_attempt > 0:
            logger.info("=" * 80)
            logger.info("MAIN BUTTON REGENERATION INITIATED")
            logger.info("=" * 80)
            logger.info("User clicked main 'Regenerate Script' button")

            # Track regeneration for feedback loop analysis
            previous_regen_count = state.script_regeneration_count
            state.track_script_regeneration("main_button_regeneration")
            logger.info(f"Main button regeneration count incremented: {previous_regen_count} → {state.script_regeneration_count}")

            # Reset validation state to force regeneration
            logger.info("Resetting validation state for regeneration:")
            logger.info(f"  - script_validation_done: {getattr(state, 'script_validation_done', False)} → False")
            logger.info(f"  - script_validation_passed: {getattr(state, 'script_validation_passed', False)} → False")

            state.script_validation_done = False
            state.script_validation_passed = False
            state.script_validation_results = {}

            # Update session state with new regeneration count
            st.session_state['regeneration_attempt'] = state.script_regeneration_count
            st.session_state['state'] = state

            logger.info(f"Updated session state regeneration_attempt to {state.script_regeneration_count}")
            logger.info("=" * 80)

        # Show enhanced spinner message for regeneration
        current_attempt = max(regeneration_attempt, state.script_regeneration_count)
        if current_attempt > 0:
            spinner_text = f"🔄 Regenerating script with feedback loop (Attempt #{current_attempt})..."

            # Create progress container
            progress_container = st.container()
            with progress_container:
                # Get current feedback guidelines count
                common_issues = state.get_common_validation_issues(limit=8) if hasattr(state, 'get_common_validation_issues') else []
                current_guidelines_count = len(common_issues)

                st.info(f"🧠 **Feedback Loop Active**: Using {current_guidelines_count} validation guidelines from previous attempts")

                # Show progress steps
                progress_bar = st.progress(0)
                status_text = st.empty()

                # Step 1: Analyzing feedback
                status_text.text("📊 Analyzing validation feedback history...")
                progress_bar.progress(20)
                import time
                time.sleep(0.5)

                # Step 2: Generating enhanced prompts
                status_text.text("✨ Generating enhanced prompts with feedback guidelines...")
                progress_bar.progress(40)
                time.sleep(0.5)

                # Step 3: Applying adaptive threshold
                status_text.text("🎯 Applying adaptive frequency threshold for recommendations...")
                progress_bar.progress(60)
                time.sleep(0.5)

                # Step 4: Calling AI with enhanced prompts
                status_text.text("🤖 Calling AI with enhanced prompts for improved script generation...")
                progress_bar.progress(80)
                time.sleep(0.5)

                status_text.text("🔄 Generating enhanced test script...")
                progress_bar.progress(100)
        else:
            spinner_text = "Generating test script for selected test case step..."

        with st.spinner(spinner_text):
                try:
                    # Import the necessary function
                    from core.ai import generate_test_script

                    # Prepare the inputs for script generation
                    test_case = state.selected_test_case
                    step = selected_step
                    step_table_entry = selected_step_table_entry

                    # Get element matches for this step
                    element_matches = []
                    if hasattr(state, 'element_matches') and state.element_matches:
                        try:
                            test_case_id = test_case.get('Test Case ID')
                            step_no = str(step.get('Step No'))
                            element_matches = state.element_matches.get(test_case_id, {}).get(step_no, [])
                            logger.info(f"Found {len(element_matches)} element matches for step {step_no}")
                        except (KeyError, TypeError) as e:
                            logger.error(f"Error retrieving element matches: {str(e)}")
                            element_matches = []

                    # Get test data for this step
                    test_data = {}
                    if hasattr(state, 'test_data') and state.test_data:
                        test_data = state.test_data
                        logger.info(f"Using test data with {len(test_data)} entries")

                        # Write test data to a file that conftest.py can read
                        try:
                            import json
                            test_data_file = "test_data_runtime.json"
                            with open(test_data_file, 'w') as f:
                                json.dump(test_data, f, indent=2)
                            logger.info(f"Wrote test data to {test_data_file} for conftest.py")
                        except Exception as e:
                            logger.warning(f"Could not write test data file: {str(e)}")

                    # Generate the test script through the centralized AI function
                    logger.info(f"Calling generate_test_script for step {step.get('Step No')} of test case {test_case.get('Test Case ID')}")

                    # Check if user has provided custom instructions for regeneration
                    custom_instructions = None
                    if hasattr(state, 'generation_custom_instructions') and state.generation_custom_instructions:
                        custom_instructions = state.generation_custom_instructions
                        logger.info(f"Stage 6: Using custom instructions for script generation ({len(custom_instructions)} characters)")

                    # Generate the test script - returns a tuple (merged_script, step_specific_script)
                    script_result = generate_test_script(
                        test_case,
                        element_matches,
                        test_data,
                        state.website_url,
                        step_table_entry,
                        state.google_api_key,
                        state,  # Pass the state for script continuity
                        custom_instructions  # Pass custom instructions for regeneration
                    )

                    # Unpack the result - handle both tuple return and legacy string return
                    if isinstance(script_result, tuple):
                        merged_script, step_specific_script = script_result
                        logger.info(f"Received both merged script ({len(merged_script)} chars) and step-specific script ({len(step_specific_script)} chars)")
                    else:
                        # For backward compatibility with older versions
                        merged_script = script_result
                        step_specific_script = None
                        logger.info(f"Received only merged script ({len(merged_script)} chars) - using legacy mode")

                    # Use the merged script as the main script content
                    script_content = merged_script

                    # Check for errors before proceeding
                    if script_content and script_content.startswith("Error"):
                        st.error(script_content)
                        logger.error(f"AI returned error: {script_content}")
                    elif not script_content:
                        st.error("AI returned empty script content")
                        logger.error("AI returned empty script content")
                        return
                    else:
                        # Create file paths for both scripts with timestamp for versioning
                        script_dir = "generated_tests"
                        os.makedirs(script_dir, exist_ok=True)

                        # Create timestamp for consistent filenames
                        timestamp = int(time.time())
                        test_case_id = test_case.get('Test Case ID', 'unknown')
                        step_no = step.get('Step No', '0')

                        # Path for the merged script
                        script_file = os.path.join(
                            script_dir,
                            f"test_{test_case_id}_{step_no}_{timestamp}_merged.py"
                        )

                        # Save the merged script to a file
                        try:
                            with open(script_file, "w") as f:
                                f.write(script_content)
                            logger.info(f"Saved merged script to {script_file}")
                        except (IOError, PermissionError) as e:
                            logger.error(f"Error saving merged script: {str(e)}")
                            st.error(f"Error saving merged script: {str(e)}")
                            return

                        # If we have a step-specific script, save it too
                        step_specific_file = None
                        if step_specific_script:
                            step_specific_file = os.path.join(
                                script_dir,
                                f"test_{test_case_id}_{step_no}_{timestamp}_step_only.py"
                            )
                            try:
                                with open(step_specific_file, "w") as f:
                                    f.write(step_specific_script)
                                logger.info(f"Saved step-specific script to {step_specific_file}")
                            except (IOError, PermissionError) as e:
                                logger.error(f"Error saving step-specific script: {str(e)}")
                                st.warning(f"Error saving step-specific script: {str(e)}")
                                # Continue with the merged script only

                        # Update state with script information
                        # Track state changes with before/after values for proper logging
                        if script_content:
                            # Update last_script_content with proper logging
                            old_script_content = getattr(state, 'last_script_content', '')
                            if old_script_content != script_content:
                                state.last_script_content = script_content
                                logger.info(f"State change: last_script_content: {len(old_script_content)} chars -> {len(script_content)} chars")

                            # Update step-specific script if available
                            if step_specific_script:
                                old_step_specific_script = getattr(state, 'last_step_specific_script', '')
                                if old_step_specific_script != step_specific_script:
                                    state.last_step_specific_script = step_specific_script
                                    logger.info(f"State change: last_step_specific_script: {len(old_step_specific_script)} chars -> {len(step_specific_script)} chars")

                                if step_specific_file:
                                    old_step_specific_file = getattr(state, 'last_step_specific_file', '')
                                    if old_step_specific_file != step_specific_file:
                                        state.last_step_specific_file = step_specific_file
                                        logger.info(f"State change: last_step_specific_file: {old_step_specific_file} -> {step_specific_file}")

                            # Update script file path
                            old_script_file = getattr(state, 'last_script_file', '')
                            if old_script_file != script_file:
                                state.last_script_file = script_file
                                logger.info(f"State change: last_script_file: {old_script_file} -> {script_file}")

                            # Update generated script path
                            old_generated_script_path = getattr(state, 'generated_script_path', '')
                            if old_generated_script_path != script_file:
                                state.generated_script_path = script_file
                                logger.info(f"State change: generated_script_path: {old_generated_script_path} -> {script_file}")

                            # Update script generation flags
                            old_script_just_generated = getattr(state, 'script_just_generated', False)
                            if not old_script_just_generated:
                                state.script_just_generated = True
                                logger.info(f"State change: script_just_generated: {old_script_just_generated} -> True")

                            old_step_ready_for_script = getattr(state, 'step_ready_for_script', False)
                            if not old_step_ready_for_script:
                                state.step_ready_for_script = True
                                logger.info(f"State change: step_ready_for_script: {old_step_ready_for_script} -> True")

                            # Update script continuity tracking
                            # This ensures proper script merging in subsequent steps
                            try:
                                state.update_script_continuity(script_content, str(step_no))
                                logger.info(f"Updated script continuity tracking for step {step_no}")
                            except Exception as e:
                                logger.error(f"Error updating script continuity: {str(e)}")

                            # Check if all steps are completed and create combined script
                            try:
                                # Check if we have all steps completed for the test case
                                if (hasattr(state, 'selected_test_case') and state.selected_test_case and
                                    hasattr(state, 'previous_scripts') and state.previous_scripts):

                                    # Get total steps for the test case - try multiple sources
                                    total_steps = 0

                                    # First try: step_table_json (most reliable for automation)
                                    if hasattr(state, 'step_table_json') and state.step_table_json:
                                        total_steps = len(state.step_table_json)
                                        logger.debug(f"Stage 6: Got total_steps from step_table_json: {total_steps}")

                                    # Second try: test case Steps (fallback)
                                    elif state.selected_test_case:
                                        test_case_steps = state.selected_test_case.get('Steps', [])
                                        total_steps = len(test_case_steps)
                                        logger.debug(f"Stage 6: Got total_steps from test case Steps: {total_steps}")

                                    # Update state.total_steps if not set
                                    if hasattr(state, 'total_steps') and state.total_steps == 0 and total_steps > 0:
                                        state.total_steps = total_steps
                                        logger.info(f"State change: total_steps = {state.total_steps}")

                                    completed_steps = len(state.previous_scripts)

                                    logger.info(f"Stage 6: Script completion check - {completed_steps}/{total_steps} steps completed")

                                    # If all steps are completed, create the final step script for Stage 8 optimization
                                    if completed_steps >= total_steps and total_steps > 1:
                                        logger.info("Stage 6: All steps completed, creating final step script for Stage 8 optimization")
                                        final_script_path = create_final_step_script(state)

                                        if final_script_path:
                                            st.success(f"🎯 Final step script created for optimization: {os.path.basename(final_script_path)}")
                                            logger.info(f"Stage 6: Successfully created final step script at {final_script_path}")

                                            # Also create the traditional combined script for reference (optional)
                                            combined_script_path = create_combined_script(state)
                                            if combined_script_path:
                                                logger.info(f"Stage 6: Also created full combined script for reference at {combined_script_path}")
                                        else:
                                            logger.warning("Stage 6: Failed to create final step script")
                                    elif completed_steps >= total_steps and total_steps == 1:
                                        # For single-step test cases, just use the single step script
                                        logger.info("Stage 6: Single step test case completed, using single step script for optimization")
                                        final_script_path = create_final_step_script(state)
                                        if final_script_path:
                                            st.success(f"🎯 Single step script ready for optimization: {os.path.basename(final_script_path)}")
                                            logger.info(f"Stage 6: Successfully prepared single step script at {final_script_path}")
                                    else:
                                        logger.info(f"Stage 6: Not all steps completed yet ({completed_steps}/{total_steps}), final step script will be created when all steps are done")
                            except Exception as e:
                                logger.error(f"Stage 6: Error checking for combined script creation: {str(e)}")

                            # Display success message and script content
                            st.success(f"✅ Test script generated successfully: {os.path.basename(script_file)}")

                            # Add script to history for Stage 9 browsing
                            try:
                                state.add_script_to_history(
                                    script_content=script_content,
                                    script_type='step',
                                    step_no=str(step_no),
                                    file_path=script_file,
                                    metadata={
                                        'test_case_id': test_case_id,
                                        'step_description': step.get('Action', 'Unknown action'),
                                        'generation_timestamp': datetime.now().isoformat(),
                                        'validation_pending': True
                                    }
                                )
                                logger.info(f"Added step script to history for browsing: step {step_no}")
                            except Exception as e:
                                logger.warning(f"Failed to add script to history: {e}")

                            # Trigger script validation after successful generation
                            # Always run validation for new scripts (including regenerated ones)
                            if not state.script_validation_done:
                                logger.info("Starting automated script validation")
                                with st.spinner("Performing automated code quality validation..."):
                                    try:
                                        # Import the validation function
                                        from core.ai import validate_generated_script

                                        # Perform validation
                                        validation_results = validate_generated_script(
                                            script_content=script_content,
                                            test_case=state.selected_test_case,
                                            step_table_entry=selected_step_table_entry,
                                            test_data=test_data,
                                            element_matches=element_matches,
                                            api_key=state.google_api_key
                                        )

                                        # Store validation results in state
                                        state.script_validation_results = validation_results
                                        state.script_validation_done = True
                                        state.script_validation_passed = validation_results.get('ready_for_execution', False)

                                        # Add validation feedback to learning history
                                        test_case_id = state.selected_test_case.get('Test Case ID', 'Unknown')
                                        step_no = selected_step_table_entry.get('step_no', '1') if selected_step_table_entry else '1'
                                        state.add_validation_feedback(validation_results, test_case_id, step_no)

                                        logger.info(f"Script validation completed - Status: {validation_results.get('validation_status', 'unknown')}")

                                    except Exception as e:
                                        logger.error(f"Error during script validation: {e}")
                                        # Set default validation results on error
                                        state.script_validation_results = {
                                            "syntax_valid": True,
                                            "quality_score": 50,  # Default middle score for validation errors
                                            "issues_found": [{"category": "validation", "severity": "medium", "description": f"Validation error: {str(e)}"}],
                                            "recommendations": ["Review script manually"],
                                            "confidence_rating": "low",
                                            "ready_for_execution": True,
                                            "validation_status": "needs_improvement",
                                            "validation_error": str(e)
                                        }
                                        state.script_validation_done = True
                                        state.script_validation_passed = True  # Allow progression despite validation error

                            # Ensure state persistence after all script generation and validation changes
                            st.session_state['state'] = state
                            logger.info("State persistence confirmed after script generation and validation")

                            # Set flag to display script in unified section (prevents duplicate display)
                            st.session_state['show_script_in_unified_section'] = True
                        else:
                            if script_content and script_content.startswith("Error"):
                                # Error already displayed above
                                pass
                            else:
                                st.error("AI script generation returned empty content.")
                                logger.error("AI script generation returned empty content")
                except (KeyError, ValueError, TypeError) as e:
                    st.error(f"Error generating test script: {str(e)}")
                    logger.error(f"Error generating test script: {str(e)}", exc_info=True)
                except Exception as e:
                    st.error(f"Unexpected error generating test script: {str(e)}")
                    import traceback
                    error_details = traceback.format_exc()
                    logger.error(f"Unexpected error generating test script: {error_details}")
                    st.error(f"Error details: {error_details}")

    # Unified script display section - shows script if available (prevents duplication)
    if (hasattr(state, 'script_just_generated') and state.script_just_generated and
        hasattr(state, 'last_script_content') and state.last_script_content and
        hasattr(state, 'last_script_file') and state.last_script_file):

        logger.info("Displaying generated script in unified section")

        # Get the script content and file info
        script_content = state.last_script_content
        script_file = state.last_script_file
        step_specific_script = getattr(state, 'last_step_specific_script', None)
        step_specific_file = getattr(state, 'last_step_specific_file', None)

        # Display success message
        st.success(f"✅ Script available: {os.path.basename(script_file)}")

        # Display validation results if available
        if (hasattr(state, 'script_validation_done') and state.script_validation_done and
            hasattr(state, 'script_validation_results') and state.script_validation_results):

            validation_results = state.script_validation_results
            validation_status = validation_results.get('validation_status', 'unknown')

            # Simple validation status display
            if validation_status == "excellent":
                st.success("✅ Validation: Excellent")
            elif validation_status == "good":
                st.success("✅ Validation: Good")
            elif validation_status == "needs_improvement":
                st.warning("⚠️ Validation: Needs Improvement")
            elif validation_status == "failed":
                st.error("❌ Validation: Failed")
            else:
                st.info("ℹ️ Validation: Unknown")

            # Show issues and recommendations only if validation failed or needs improvement
            if validation_status in ["needs_improvement", "failed"]:
                issues = validation_results.get('issues_found', [])
                recommendations = validation_results.get('recommendations', [])

                if issues or recommendations:
                    with st.expander("Validation Details", expanded=False):
                        if issues:
                            st.markdown("**Issues:**")
                            for issue in issues[:3]:  # Show only top 3 issues
                                st.markdown(f"• {issue.get('description', 'No description')}")

                        if recommendations:
                            st.markdown("**Recommendations:**")
                            for rec in recommendations[:3]:  # Show only top 3 recommendations
                                st.markdown(f"• {rec}")

        # Display the script with editor functionality
        from ui_components.script_editor import render_script_editor, render_script_editor_help

        # Render the script editor component
        script_modified = render_script_editor(state, script_content, script_file)

        # Show help section
        render_script_editor_help()

        # Show additional script views only if step-specific script is available
        if step_specific_script and step_specific_file:
            with st.expander("Additional Script Views", expanded=False):
                script_tabs = st.tabs(["Step-Specific Script", "Diff View"])

                with script_tabs[0]:
                    st.code(step_specific_script, language="python")

                with script_tabs[1]:
                    try:
                        from core.helpers_diff import generate_html_diff
                        html_diff = generate_html_diff(step_specific_script, script_content)
                        st.components.v1.html(html_diff, height=400, scrolling=True)
                    except Exception as e:
                        st.error(f"Error generating diff view: {str(e)}")

        # User feedback section for script improvement
        st.markdown("### 💬 Script Improvement Feedback")

        # Check if user wants to provide feedback or if there are issues
        show_feedback_section = st.checkbox("💬 Provide feedback for script improvement", key="show_generation_feedback_input")

        if show_feedback_section:
            with st.expander("✨ Provide Custom Instructions", expanded=True):
                st.markdown("""
                **Help improve the script generation:** Share your feedback, specific requirements,
                or issues you've noticed. This will be used to regenerate a better test script.
                """)

                # User comment input
                user_comment = st.text_area(
                    "Your feedback/instructions:",
                    value=getattr(state, 'user_generation_comment', ''),
                    height=100,
                    placeholder="e.g., 'Add more error handling', 'Use explicit waits', 'Improve element locators', etc.",
                    key="user_generation_comment_input",
                    help="Describe what you'd like to improve in the generated script"
                )

                # Store user comment in state
                if user_comment != getattr(state, 'user_generation_comment', ''):
                    state.user_generation_comment = user_comment
                    state.generation_comment_enhancement_done = False  # Reset enhancement flag when comment changes
                    state.ai_enhanced_generation_comment = ""
                    state.use_enhanced_generation_comment = False
                    st.session_state['state'] = state

                # AI Enhancement section
                if user_comment.strip():
                    st.markdown("---")
                    st.markdown("**✨ AI Enhancement (Optional)**")

                    col1, col2 = st.columns([1, 1])

                    with col1:
                        if st.button("✨ Enhance Comment with AI",
                                   use_container_width=True,
                                   key="enhance_generation_comment_ai",
                                   help="Let AI enhance your feedback into detailed technical instructions"):
                            logger.info("Stage 6: User clicked 'Enhance Comment with AI' button")

                            with st.spinner("Enhancing your feedback with AI..."):
                                try:
                                    from core.ai import enhance_generation_comment_with_ai

                                    enhanced_comment = enhance_generation_comment_with_ai(
                                        user_comment=user_comment,
                                        test_case=state.selected_test_case,
                                        step_table_entry=state.selected_step_table_entry,
                                        api_key=state.google_api_key,
                                        context={
                                            'test_case_id': state.selected_test_case.get('Test Case ID', 'unknown'),
                                            'generation_stage': 'stage6_regeneration'
                                        }
                                    )

                                    # Store enhanced comment in state
                                    state.ai_enhanced_generation_comment = enhanced_comment
                                    state.generation_comment_enhancement_done = True
                                    st.session_state['state'] = state

                                    logger.info(f"Stage 6: AI comment enhancement completed, enhanced length: {len(enhanced_comment)}")
                                    st.success("✅ Comment enhanced successfully!")
                                    st.rerun()

                                except Exception as e:
                                    error_msg = f"Error enhancing comment: {str(e)}"
                                    st.error(error_msg)
                                    logger.error(f"Stage 6: {error_msg}")

                    # Show enhanced comment if available
                    if (hasattr(state, 'generation_comment_enhancement_done') and state.generation_comment_enhancement_done and
                        hasattr(state, 'ai_enhanced_generation_comment') and state.ai_enhanced_generation_comment):

                        st.markdown("**📝 Enhanced Instructions:**")

                        # Show both versions for comparison
                        col1, col2 = st.columns(2)

                        with col1:
                            st.markdown("**Your Original Feedback:**")
                            st.info(user_comment)

                        with col2:
                            st.markdown("**AI-Enhanced Instructions:**")
                            st.success(state.ai_enhanced_generation_comment)

                        # User choice between original and enhanced
                        st.markdown("**Choose which version to use for regeneration:**")

                        comment_choice = st.radio(
                            "Select instructions to use:",
                            options=["Use Original Feedback", "Use AI-Enhanced Instructions"],
                            index=1 if getattr(state, 'use_enhanced_generation_comment', False) else 0,
                            key="generation_comment_choice_radio",
                            horizontal=True
                        )

                        # Update state based on user choice
                        use_enhanced = comment_choice == "Use AI-Enhanced Instructions"
                        if use_enhanced != getattr(state, 'use_enhanced_generation_comment', False):
                            state.use_enhanced_generation_comment = use_enhanced
                            st.session_state['state'] = state

            # Check if user has provided custom instructions
            has_custom_instructions = (
                (hasattr(state, 'user_generation_comment') and state.user_generation_comment.strip()) or
                (hasattr(state, 'ai_enhanced_generation_comment') and state.ai_enhanced_generation_comment.strip())
            )

            # Show regeneration button if custom instructions are available
            if has_custom_instructions:
                st.markdown("---")

                # Get regeneration attempts
                try:
                    from core.config import MAX_REGENERATIONS
                except ImportError:
                    MAX_REGENERATIONS = 3

                regen_attempts = getattr(state, 'regen_attempts', 0)
                regeneration_limit_reached = regen_attempts >= MAX_REGENERATIONS

                if regeneration_limit_reached:
                    st.error(f"🔄 Regeneration limit reached ({regen_attempts}/{MAX_REGENERATIONS})")
                    if st.button("Reset Counter", key="reset_generation_counter", use_container_width=True):
                        state.regen_attempts = 0
                        logger.info("User reset generation regeneration counter")
                        st.success("✅ Counter reset")
                        st.rerun()
                else:
                    button_text = f"🔄 Regenerate with Feedback ({regen_attempts + 1}/{MAX_REGENERATIONS})"
                    if st.button(button_text, key="regenerate_with_feedback", use_container_width=True, type="secondary"):
                        logger.info("=" * 80)
                        logger.info("CUSTOM FEEDBACK REGENERATION INITIATED")
                        logger.info("=" * 80)
                        logger.info("User clicked 'Regenerate with Feedback' button")

                        # Determine which custom instructions to use
                        custom_instructions = None
                        if hasattr(state, 'user_generation_comment') and state.user_generation_comment.strip():
                            if hasattr(state, 'use_enhanced_generation_comment') and state.use_enhanced_generation_comment and hasattr(state, 'ai_enhanced_generation_comment'):
                                custom_instructions = state.ai_enhanced_generation_comment
                                logger.info("Stage 6: Using AI-enhanced comment for regeneration")
                            else:
                                custom_instructions = state.user_generation_comment
                                logger.info("Stage 6: Using original user comment for regeneration")

                            logger.info(f"Stage 6: Custom instructions length: {len(custom_instructions)} characters")

                        # Store custom instructions for use during generation
                        if custom_instructions:
                            state.generation_custom_instructions = custom_instructions
                            logger.info("Stage 6: Stored custom instructions for regeneration")
                        else:
                            state.generation_custom_instructions = None
                            logger.info("Stage 6: No custom instructions provided for regeneration")

                        # Increment regeneration attempts counter
                        if not hasattr(state, 'regen_attempts'):
                            state.regen_attempts = 0
                        state.regen_attempts += 1
                        logger.info(f"Generation regeneration attempts incremented: {state.regen_attempts}/{MAX_REGENERATIONS}")

                        # Track regeneration for feedback loop analysis
                        previous_regen_count = getattr(state, 'script_regeneration_count', 0)
                        state.track_script_regeneration("custom_feedback_regeneration")
                        logger.info(f"Regeneration count incremented: {previous_regen_count} → {state.script_regeneration_count}")

                        # Reset script generation flags to ensure regeneration occurs
                        logger.info("Resetting script generation flags for custom feedback regeneration")
                        state.script_just_generated = False

                        # Set regeneration flags for UI feedback
                        st.session_state['regeneration_in_progress'] = True
                        st.session_state['regeneration_attempt'] = state.script_regeneration_count

                        # Ensure state is properly persisted before rerun
                        st.session_state['state'] = state
                        st.session_state['stage_progression_message'] = f"🔄 Regenerating script with custom feedback (Attempt #{state.script_regeneration_count})."

                        logger.info(f"Custom feedback regeneration initiated - attempt #{state.script_regeneration_count}")
                        logger.info("Session state updated for custom feedback regeneration workflow")
                        logger.info("State persistence confirmed before rerun")
                        logger.info("=" * 80)

                        # Show immediate feedback to user
                        st.success(f"🔄 Regeneration initiated with custom feedback (Attempt #{state.script_regeneration_count}).")
                        st.info("🔄 Page will refresh to start regeneration with your custom instructions...")

                        # Force state persistence and rerun
                        st.rerun()

        # Workflow progression section
        st.subheader("Next Steps")

        # Simple regeneration status
        try:
            from core.config import MAX_REGENERATIONS
        except ImportError:
            MAX_REGENERATIONS = 3

        if hasattr(state, 'regen_attempts') and state.regen_attempts > 0:
            if state.regen_attempts >= MAX_REGENERATIONS:
                st.error(f"🔄 Regeneration limit reached ({state.regen_attempts}/{MAX_REGENERATIONS})")
            else:
                st.info(f"🔄 Regenerations: {state.regen_attempts}/{MAX_REGENERATIONS}")

        # Simple action buttons
        col1, col2 = st.columns(2)

        with col1:
            if st.button("Proceed to Stage 7", key="proceed_stage7_unified", use_container_width=True, type="primary"):
                logger.info("User chose to proceed to Stage 7")

                # Reset regeneration counter when proceeding to Stage 7
                if hasattr(state, 'regen_attempts') and state.regen_attempts > 0:
                    logger.info(f"Resetting regeneration counter: {state.regen_attempts} → 0")
                    state.regen_attempts = 0

                # Advance to Stage 7
                if state.current_stage == StateStage.STAGE6_GENERATE:
                    state.advance_to(StateStage.STAGE7_EXECUTE, "Script generated - advancing to Stage 7")
                    st.session_state['state'] = state
                    st.session_state['stage_progression_message'] = "✅ Proceeding to Stage 7."
                    st.rerun()
                    return

        with col2:
            # Check if regeneration limit has been reached
            regeneration_limit_reached = hasattr(state, 'regen_attempts') and state.regen_attempts >= MAX_REGENERATIONS

            if regeneration_limit_reached:
                if st.button("Reset Counter", key="reset_regen_counter_unified", use_container_width=True):
                    state.regen_attempts = 0
                    logger.info("User reset regeneration counter")
                    st.success("✅ Counter reset")
                    st.rerun()
            else:
                regen_attempts = getattr(state, 'regen_attempts', 0)
                button_text = f"Regenerate ({regen_attempts + 1}/{MAX_REGENERATIONS})"
                if st.button(button_text, key="regenerate_script_unified", use_container_width=True):
                        logger.info("=" * 80)
                        logger.info("FEEDBACK LOOP REGENERATION INITIATED")
                        logger.info("=" * 80)
                        logger.info("User clicked 'Regenerate Script' button after validation")

                        # Increment regeneration attempts counter
                        if not hasattr(state, 'regen_attempts'):
                            state.regen_attempts = 0
                        state.regen_attempts += 1
                        logger.info(f"Regeneration attempts incremented: {state.regen_attempts}/{MAX_REGENERATIONS}")

                        # Log current validation feedback state
                        feedback_history_count = len(state.validation_feedback_history) if hasattr(state, 'validation_feedback_history') else 0
                        logger.info(f"Current validation feedback history: {feedback_history_count} entries")

                        if hasattr(state, 'validation_feedback_history') and state.validation_feedback_history:
                            latest_feedback = state.validation_feedback_history[-1]
                            logger.info(f"Latest feedback quality score: {latest_feedback.get('quality_score', 'N/A')}")
                            logger.info(f"Latest feedback issues count: {len(latest_feedback.get('issues_found', []))}")
                            logger.info(f"Latest feedback recommendations count: {len(latest_feedback.get('recommendations', []))}")

                        # Track regeneration for feedback loop analysis
                        previous_regen_count = getattr(state, 'script_regeneration_count', 0)
                        state.track_script_regeneration("validation_feedback")
                        logger.info(f"Regeneration count incremented: {previous_regen_count} → {state.script_regeneration_count}")

                        # Log state reset operations
                        logger.info("Resetting validation state for regeneration:")
                        logger.info(f"  - script_validation_done: {state.script_validation_done} → False")
                        logger.info(f"  - script_validation_passed: {state.script_validation_passed} → False")
                        logger.info(f"  - script_validation_results: {len(state.script_validation_results)} entries → 0")

                        # Reset validation state to trigger new generation with enhanced feedback
                        state.script_validation_done = False
                        state.script_validation_passed = False
                        state.script_validation_results = {}

                        # Reset script generation flags to ensure regeneration occurs
                        logger.info("Resetting script generation flags for regeneration:")
                        logger.info(f"  - script_just_generated: {getattr(state, 'script_just_generated', False)} → False")

                        state.script_just_generated = False

                        # Note: We keep generated_script_path and last_script_content to preserve context
                        # but reset the validation state to trigger new generation

                        # Log feedback loop confirmation
                        common_issues = state.get_common_validation_issues(limit=8) if hasattr(state, 'get_common_validation_issues') else []
                        logger.info(f"Enhanced prompts will include {len(common_issues)} validation guidelines")
                        logger.info("Confirmed: Enhanced prompt generation with feedback loop will be used")

                        # Set regeneration flags for UI feedback
                        st.session_state['regeneration_in_progress'] = True
                        st.session_state['regeneration_attempt'] = state.script_regeneration_count
                        st.session_state['feedback_guidelines_count'] = len(common_issues)

                        # Ensure state is properly persisted before rerun
                        st.session_state['state'] = state
                        st.session_state['stage_progression_message'] = f"🔄 Regenerating script (Attempt #{state.script_regeneration_count}) with {len(common_issues)} feedback guidelines."

                        logger.info(f"Script regeneration initiated - attempt #{state.script_regeneration_count}")
                        logger.info("Session state updated for regeneration workflow")
                        logger.info("State persistence confirmed before rerun")
                        logger.info("=" * 80)

                        # Show immediate feedback to user
                        st.success(f"🔄 Regeneration initiated (Attempt #{state.script_regeneration_count}) with {len(common_issues)} feedback guidelines.")
                        st.info("🔄 Page will refresh to start regeneration with enhanced prompts...")

                        # Force state persistence and rerun
                        st.rerun()
